class AnalyticsEvents {
  // App lifecycle events
  static const String appStart = 'app_start';
  static const String appClose = 'app_close';
  static const String sessionStart = 'session_start';

  // Preset events
  static const String addPreset = 'add_preset';
  static const String editPreset = 'edit_preset';
  static const String removePreset = 'remove_preset';
  static const String playPreset = 'play_preset';

  // Video playback events
  static const String videoPlay = 'video_play';
  static const String videoPause = 'video_pause';
  static const String videoStop = 'video_stop';
  static const String videoSeek = 'video_seek';
  static const String videoComplete = 'video_complete';
  static const String videoError = 'video_error';

  // Source management events
  static const String sourceSwitch = 'source_switch';
  static const String sourceFailover = 'source_failover';
  static const String sourceLoad = 'source_load';

  // Quality and settings events
  static const String qualityChange = 'quality_change';
  static const String audioTrackChange = 'audio_track_change';
  static const String subtitleChange = 'subtitle_change';
  static const String playbackSpeedChange = 'playback_speed_change';

  // UI interaction events
  static const String themeToggle = 'theme_toggle';
  static const String fullscreenEnter = 'fullscreen_enter';
  static const String fullscreenExit = 'fullscreen_exit';
  static const String pipEnter = 'pip_enter';
  static const String pipExit = 'pip_exit';

  // Deeplink events
  static const String deeplinkReceived = 'l_received';
  static const String deeplinkProcessed = 'l_processed';
  static const String deeplinkError = 'l_error';
}
