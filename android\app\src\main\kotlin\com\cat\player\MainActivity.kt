package com.cat.player

import android.content.Intent
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "cat_player/deeplink"
    private var initialDeeplink: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Handle initial intent
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleIntent(intent)
    }

    override fun configureFlutterEngine(flutterEngine: io.flutter.embedding.engine.FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getInitialDeeplink" -> {
                    result.success(initialDeeplink)
                    initialDeeplink = null // Clear after first use
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun handleIntent(intent: Intent?) {
        if (intent?.action == Intent.ACTION_VIEW) {
            val data = intent.data
            if (data != null && data.scheme == "catplayer") {
                val deeplinkUrl = data.toString()
                android.util.Log.d("MainActivity", "Received deeplink (length: ${deeplinkUrl.length}): $deeplinkUrl")
                android.util.Log.d("MainActivity", "Full URI data: scheme=${data.scheme}, host=${data.host}, query=${data.query}")

                // If Flutter engine is ready, send immediately
                flutterEngine?.let { engine ->
                    android.util.Log.d("MainActivity", "Flutter engine ready, sending deeplink immediately")
                    MethodChannel(engine.dartExecutor.binaryMessenger, CHANNEL)
                        .invokeMethod("handleDeeplink", deeplinkUrl)
                } ?: run {
                    // Store for later when Flutter engine is ready
                    android.util.Log.d("MainActivity", "Flutter engine not ready, storing deeplink for later")
                    initialDeeplink = deeplinkUrl
                }
            }
        }
    }
}
