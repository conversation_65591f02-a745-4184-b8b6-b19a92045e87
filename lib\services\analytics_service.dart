import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

class AnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  FirebaseAnalyticsObserver getAnalyticsObserver() =>
      FirebaseAnalyticsObserver(analytics: _analytics);

  Future<void> logEvent(String name, {Map<String, dynamic>? parameters}) async {
    try {
      // Log to console in debug mode
      if (kDebugMode) {
        debugPrint(
            'Analytics Event: $name ${parameters != null ? 'with parameters: $parameters' : ''}');
      }

      await _analytics.logEvent(
        name: name,
        parameters: parameters,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to log analytics event $name: $e');
      }
    }
  }

  // Convenience methods for common events
  Future<void> logVideoEvent(
    String eventName, {
    String? videoName,
    String? videoUrl,
    int? sourceIndex,
    Duration? position,
    String? quality,
    String? errorMessage,
  }) async {
    final parameters = <String, dynamic>{};

    if (videoName != null) parameters['video_name'] = videoName;
    if (videoUrl != null) parameters['video_url'] = videoUrl;
    if (sourceIndex != null) parameters['source_index'] = sourceIndex;
    if (position != null) parameters['position_seconds'] = position.inSeconds;
    if (quality != null) parameters['quality'] = quality;
    if (errorMessage != null) parameters['error_message'] = errorMessage;

    await logEvent(eventName,
        parameters: parameters.isNotEmpty ? parameters : null);
  }

  Future<void> logPresetEvent(
    String eventName, {
    String? presetName,
    int? sourceCount,
    String? presetId,
  }) async {
    final parameters = <String, dynamic>{};

    if (presetName != null) parameters['preset_name'] = presetName;
    if (sourceCount != null) parameters['source_count'] = sourceCount;
    if (presetId != null) parameters['preset_id'] = presetId;

    await logEvent(eventName,
        parameters: parameters.isNotEmpty ? parameters : null);
  }

  Future<void> logUIEvent(
    String eventName, {
    String? screenName,
    String? elementName,
    Map<String, dynamic>? additionalParams,
  }) async {
    final parameters = <String, dynamic>{};

    if (screenName != null) parameters['screen_name'] = screenName;
    if (elementName != null) parameters['element_name'] = elementName;
    if (additionalParams != null) parameters.addAll(additionalParams);

    await logEvent(eventName,
        parameters: parameters.isNotEmpty ? parameters : null);
  }
}
