import 'package:cat_player/models/preset_item.dart';
import 'package:cat_player/screens/video_player_screen.dart';
import 'package:flutter/material.dart' hide NavigationDrawer;
import 'package:provider/provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../providers/theme_provider.dart';
import '../widgets/navigation_drawer.dart';
import '../widgets/preset_list_widget.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/preset_dialog.dart';
import '../services/analytics_service.dart';
import '../services/analytics_events.dart';
import '../services/admob_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<PresetItem> _presetList = [];
  List<PresetItem> _filteredPresetList = [];
  PresetItem? _currentPreset;
  bool _isLoading = false;
  final TextEditingController _searchController = TextEditingController();
  final AnalyticsService _analyticsService = AnalyticsService();

  @override
  void initState() {
    super.initState();
    _filteredPresetList = _presetList;
    _searchController.addListener(_filterPresets);
    _searchController
        .addListener(() => setState(() {})); // For clear button visibility
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterPresets() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredPresetList = _presetList;
      } else {
        _filteredPresetList = _presetList
            .where((preset) => preset.name.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {}); // Trigger rebuild to update the clear button visibility
  }

  void _addPreset() {
    showDialog(
      context: context,
      builder: (context) => PresetDialog(
        onSave: (newPreset) {
          setState(() {
            _presetList.add(newPreset);
            _filterPresets();
            if (_currentPreset == null) {
              _currentPreset = newPreset;
            }
          });

          // Log preset creation event
          _analyticsService.logPresetEvent(
            AnalyticsEvents.addPreset,
            presetName: newPreset.name,
            sourceCount: newPreset.sources.length,
            presetId: newPreset.id,
          );
        },
      ),
    );
  }

  void _editPreset(PresetItem preset) {
    showDialog(
      context: context,
      builder: (context) => PresetDialog(
        preset: preset,
        onSave: (updatedPreset) {
          setState(() {
            final index = _presetList.indexOf(preset);
            if (index != -1) {
              _presetList[index] = updatedPreset;
              _filterPresets();
              if (_currentPreset == preset) {
                _currentPreset = updatedPreset;
              }
            }
          });

          // Log preset edit event
          _analyticsService.logPresetEvent(
            AnalyticsEvents.editPreset,
            presetName: updatedPreset.name,
            sourceCount: updatedPreset.sources.length,
            presetId: updatedPreset.id,
          );
        },
      ),
    );
  }

  void _selectPreset(PresetItem preset) {
    setState(() {
      _currentPreset = preset;
    });

    // Log preset selection event
    _analyticsService.logPresetEvent(
      AnalyticsEvents.playPreset,
      presetName: preset.name,
      sourceCount: preset.sources.length,
      presetId: preset.id,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => VideoPlayerScreen(
          videoItem: preset,
        ),
      ),
    );
  }

  void _removePreset(PresetItem preset) {
    // Log preset removal event
    _analyticsService.logPresetEvent(
      AnalyticsEvents.removePreset,
      presetName: preset.name,
      sourceCount: preset.sources.length,
      presetId: preset.id,
    );

    setState(() {
      _presetList.remove(preset);
      _filterPresets();
      if (_currentPreset == preset) {
        _currentPreset = _presetList.isNotEmpty ? _presetList.first : null;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cat Player'),
        bottom: SearchBarWidget(
          searchController: _searchController,
          onClearSearch: _clearSearch,
          onAddPreset: _addPreset,
        ),
        actions: [
          IconButton(
            icon: Icon(
                themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              _analyticsService.logUIEvent(
                AnalyticsEvents.themeToggle,
                screenName: 'home',
                additionalParams: {
                  'new_theme': themeProvider.isDarkMode ? 'light' : 'dark',
                },
              );
              themeProvider.toggleTheme();
            },
            tooltip: 'Toggle theme',
          ),
        ],
      ),
      drawer: const NavigationDrawer(),
      body: Column(
        children: [
          // Main content
          Expanded(
            child: Container(
              decoration: Theme.of(context).brightness == Brightness.dark
                  ? BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color.fromARGB(255, 20, 20, 20),
                          const Color.fromARGB(255, 50, 0, 50),
                        ],
                      ),
                    )
                  : null,
              margin: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              child: PresetListWidget(
                filteredPresetList: _filteredPresetList,
                presetList: _presetList,
                currentPreset: _currentPreset,
                isLoading: _isLoading,
                onSelectPreset: _selectPreset,
                onEditPreset: _editPreset,
                onRemovePreset: _removePreset,
              ),
            ),
          ),
          // Banner Ad
          if (AdMobService.instance.isBannerAdLoaded &&
              AdMobService.instance.bannerAd != null)
            Container(
              alignment: Alignment.center,
              width: AdMobService.instance.bannerAd!.size.width.toDouble(),
              height: AdMobService.instance.bannerAd!.size.height.toDouble(),
              child: AdWidget(ad: AdMobService.instance.bannerAd!),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addPreset,
        tooltip: 'Add Preset',
        child: const Icon(Icons.add),
      ),
    );
  }
}
