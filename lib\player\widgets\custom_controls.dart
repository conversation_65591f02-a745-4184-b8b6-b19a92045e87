import 'package:flutter/material.dart';
import '../../models/preset_item.dart';
import '../controllers/custom_player_controller.dart';
import '../themes/player_theme.dart';
import 'menus/audio_selection_menu.dart';
import 'menus/playback_speed_selection_menu.dart';
import 'menus/quality_selection_menu.dart';
import 'menus/subtitle_selection_menu.dart';
import 'source_selection_widget.dart';

/// Custom video player controls with modern UI design
class CustomControls extends StatefulWidget {
  final CustomPlayerController controller;
  final String? title;
  final bool showTitle;
  final VoidCallback? onFullscreenPressed;
  final PresetItem? videoItem;
  final int? currentSourceIndex;
  final Function(int)? onSourceSelected;

  const CustomControls({
    super.key,
    required this.controller,
    this.title,
    this.showTitle = true,
    this.onFullscreenPressed,
    this.videoItem,
    this.currentSourceIndex,
    this.onSourceSelected,
  });

  @override
  State<CustomControls> createState() => _CustomControlsState();
}

class _CustomControlsState extends State<CustomControls>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _settingsButtonController;
  late Animation<double> _fadeAnimation;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: PlayerTheme.fadeInDuration,
      reverseDuration: PlayerTheme.fadeOutDuration,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _settingsButtonController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Start the subtle pulse animation
    _settingsButtonController.repeat(reverse: true);

    widget.controller.addListener(_onControllerChanged);
    _updateFadeAnimation();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onControllerChanged);
    _fadeController.dispose();
    _settingsButtonController.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    _updateFadeAnimation();
  }

  void _updateFadeAnimation() {
    if (widget.controller.isControlsVisible || _isDragging) {
      _fadeController.forward();
    } else {
      _fadeController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        return GestureDetector(
          onTap: widget.controller.toggleControls,
          behavior: HitTestBehavior.opaque,
          child: Container(
            width: double.infinity,
            height: double.infinity,
            child: Stack(
              children: [
                // Top gradient and title
                if (widget.showTitle && widget.title != null) _buildTopBar(),

                // Center play/pause button
                _buildCenterControls(),

                // Bottom controls
                _buildBottomControls(),

                // Loading indicator
                if (widget.controller.isBuffering) _buildLoadingIndicator(),

                // Error overlay
                if (widget.controller.hasError) _buildErrorOverlay(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          height: 80,
          decoration: const BoxDecoration(
            gradient: PlayerTheme.topGradient,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_rounded),
                    color: PlayerTheme.buttonColor,
                    onPressed: () {
                      // Check if we're in fullscreen mode
                      if (widget.controller.isFullscreen) {
                        // Exit fullscreen first, then exit player
                        Navigator.of(context).pop(); // Exit fullscreen
                        Navigator.of(context).pop(); // Exit player screen
                      } else {
                        // Just exit the player screen
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      widget.title ?? '',
                      style: PlayerTheme.titleTextStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // Source selection button (moved to top right)
                  if (widget.videoItem != null &&
                      widget.videoItem!.sources.length > 1 &&
                      widget.onSourceSelected != null)
                    IconButton(
                      icon: const Icon(Icons.video_library),
                      color: PlayerTheme.buttonColor,
                      onPressed: () {
                        SourceSelectionBottomSheet.show(
                          context,
                          widget.videoItem!,
                          widget.currentSourceIndex ?? 0,
                          widget.onSourceSelected!,
                        );
                      },
                      tooltip: 'Select video source',
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCenterControls() {
    return Center(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildControlButton(
              icon: PlayerTheme.replay10Icon,
              onPressed: () => widget.controller.seekBackward(),
              size: PlayerTheme.smallButtonSize,
            ),
            const SizedBox(width: 24),
            _buildPlayPauseButton(),
            const SizedBox(width: 24),
            _buildControlButton(
              icon: PlayerTheme.forward10Icon,
              onPressed: () => widget.controller.seekForward(),
              size: PlayerTheme.smallButtonSize,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayPauseButton() {
    return Container(
      width: PlayerTheme.buttonSize + 16,
      height: PlayerTheme.buttonSize + 16,
      decoration: BoxDecoration(
        color: PlayerTheme.controlBackgroundActive,
        shape: BoxShape.circle,
        boxShadow: PlayerTheme.buttonShadow,
      ),
      child: IconButton(
        icon: Icon(
          widget.controller.isPlaying
              ? PlayerTheme.pauseIcon
              : PlayerTheme.playIcon,
        ),
        color: PlayerTheme.buttonColor,
        iconSize: PlayerTheme.iconSize + 4,
        onPressed: widget.controller.togglePlayPause,
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    double size = PlayerTheme.buttonSize,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: PlayerTheme.controlBackground,
        shape: BoxShape.circle,
        boxShadow: PlayerTheme.buttonShadow,
      ),
      child: IconButton(
        icon: Icon(icon),
        color: PlayerTheme.buttonColor,
        iconSize: PlayerTheme.iconSize,
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildBottomControls() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          height: PlayerTheme.controlBarHeight + 40,
          decoration: const BoxDecoration(
            gradient: PlayerTheme.controlsGradient,
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Progress bar
                _buildProgressBar(),
                const SizedBox(height: 8),
                // Control buttons row
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      // Time display
                      Text(
                        CustomPlayerController.formatDuration(
                            widget.controller.position),
                        style: PlayerTheme.timeTextStyle,
                      ),
                      const Spacer(),
                      // Volume button
                      IconButton(
                        icon: Icon(
                          widget.controller.isMuted
                              ? PlayerTheme.volumeOffIcon
                              : PlayerTheme.volumeUpIcon,
                        ),
                        color: PlayerTheme.buttonColor,
                        onPressed: widget.controller.toggleMute,
                      ),
                      // Quality selection
                      QualitySelectionMenu(controller: widget.controller),
                      // Playback speed selection
                      PlaybackSpeedSelectionMenu(controller: widget.controller),
                      // Audio selection
                      AudioSelectionMenu(controller: widget.controller),
                      // Subtitle selection
                      SubtitleSelectionMenu(controller: widget.controller),
                      // PiP button
                      FutureBuilder<bool>(
                        future: widget.controller.isPipAvailable(),
                        builder: (context, snapshot) {
                          final isPipAvailable = snapshot.data ?? false;
                          return isPipAvailable
                              ? IconButton(
                                  icon: Icon(PlayerTheme.pipIcon),
                                  color: widget.controller.isPipMode
                                      ? PlayerTheme.buttonColorActive
                                      : PlayerTheme.buttonColor,
                                  onPressed: widget.controller.togglePipMode,
                                )
                              : const SizedBox.shrink();
                        },
                      ),
                      // Fullscreen button
                      IconButton(
                        icon: Icon(
                          widget.controller.isFullscreen
                              ? PlayerTheme.fullscreenExitIcon
                              : PlayerTheme.fullscreenIcon,
                        ),
                        color: PlayerTheme.buttonColor,
                        onPressed: widget.onFullscreenPressed ??
                            widget.controller.toggleFullscreen,
                      ),
                      // Duration display
                      Text(
                        CustomPlayerController.formatDuration(
                            widget.controller.duration),
                        style: PlayerTheme.timeTextStyle,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: SliderTheme(
              data: SliderTheme.of(context).copyWith(
                trackHeight: PlayerTheme.progressBarHeight,
                thumbShape: const RoundSliderThumbShape(
                  enabledThumbRadius: PlayerTheme.progressThumbSize / 2,
                ),
                overlayShape: const RoundSliderOverlayShape(
                  overlayRadius: PlayerTheme.progressThumbSize,
                ),
                activeTrackColor: PlayerTheme.progressPlayed,
                inactiveTrackColor: PlayerTheme.progressBackground,
                thumbColor: PlayerTheme.progressThumb,
                overlayColor: PlayerTheme.primaryColor.withValues(alpha: .2),
              ),
              child: Slider(
                value: widget.controller.progress.clamp(0.0, 1.0),
                onChanged: (value) {
                  setState(() {
                    _isDragging = true;
                  });
                  final position = Duration(
                    milliseconds:
                        (value * widget.controller.duration.inMilliseconds)
                            .round(),
                  );
                  widget.controller.seekTo(position);
                },
                onChangeStart: (value) {
                  setState(() {
                    _isDragging = true;
                  });
                  widget.controller.showControls();
                },
                onChangeEnd: (value) {
                  setState(() {
                    _isDragging = false;
                  });
                  widget.controller.showControlsTemporarily();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(PlayerTheme.primaryColor),
        strokeWidth: 3,
      ),
    );
  }

  Widget _buildErrorOverlay() {
    return Container(
      color: PlayerTheme.overlayColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline_rounded,
              color: PlayerTheme.errorColor,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Playback Error',
              style: PlayerTheme.errorTextStyle,
            ),
            const SizedBox(height: 8),
            if (widget.controller.errorMessage != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  widget.controller.errorMessage!,
                  style: PlayerTheme.subtitleTextStyle,
                  textAlign: TextAlign.center,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // Retry logic would go here
                widget.controller.play();
              },
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: PlayerTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
