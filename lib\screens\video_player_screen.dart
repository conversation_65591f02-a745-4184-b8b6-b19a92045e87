import 'package:cat_player/models/preset_item.dart';
import 'package:cat_player/player/widgets/video_player_widget.dart';
import 'package:flutter/material.dart';
import '../services/admob_service.dart';
import '../services/session_tracking_service.dart';

class VideoPlayerScreen extends StatefulWidget {
  final PresetItem videoItem;

  const VideoPlayerScreen({super.key, required this.videoItem});

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  bool _showPrerollAd = true;
  bool _isLoadingAd = false;

  @override
  void initState() {
    super.initState();
    _showPrerollAdIfAvailable();
  }

  Future<void> _showPrerollAdIfAvailable() async {
    if (!AdMobService.instance.isRewardedAdLoaded) {
      // If no ad is available, proceed directly to video
      _proceedToVideo();
      return;
    }

    setState(() {
      _isLoadingAd = true;
    });

    final adShown = await AdMobService.instance.showRewardedAd(
      onUserEarnedReward: () {
        debugPrint('User earned reward from preroll ad');
      },
      onAdClosed: () {
        _proceedToVideo();
      },
    );

    if (!adShown) {
      // If ad failed to show, proceed to video
      _proceedToVideo();
    }
  }

  void _proceedToVideo() {
    setState(() {
      _showPrerollAd = false;
      _isLoadingAd = false;
    });
    // Start video session tracking
    SessionTrackingService.instance.startVideo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _showPrerollAd
            ? _buildPrerollScreen()
            : VideoPlayerWidget(videoItem: widget.videoItem),
      ),
    );
  }

  Widget _buildPrerollScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.play_circle_outline,
            color: Colors.white,
            size: 80,
          ),
          const SizedBox(height: 20),
          Text(
            'Loading ${widget.videoItem.name}...',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          if (_isLoadingAd)
            const Column(
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(height: 10),
                Text(
                  'Loading ad...',
                  style: TextStyle(color: Colors.white70),
                ),
              ],
            )
          else
            ElevatedButton(
              onPressed: _proceedToVideo,
              child: const Text('Skip Ad'),
            ),
        ],
      ),
    );
  }
}
