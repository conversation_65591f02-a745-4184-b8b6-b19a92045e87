@echo off
echo Installing Cat Player APK...
adb install -r build\app\outputs\flutter-apk\app-debug.apk

echo.
echo Testing deeplink...
adb shell am start -a android.intent.action.VIEW -d "catplayer://play?name=Test%%20Video&src1=https://flipfit-cdn.akamaized.net/flip_hls/661f570aab9d840019942b80-473e0b/video_h1.m3u8"

echo.
echo Check the device for the Cat Player app opening with the video.
echo If it doesn't work, check the logs with: adb logcat | findstr "MainActivity\|DeeplinkService\|MyApp"
pause
