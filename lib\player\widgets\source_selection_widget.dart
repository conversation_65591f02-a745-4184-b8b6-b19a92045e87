import 'package:flutter/material.dart';
import '../../models/preset_item.dart';
import '../../models/video_source.dart';
import '../themes/player_theme.dart';

/// Widget to display and select video sources
class SourceSelectionWidget extends StatelessWidget {
  final PresetItem videoItem;
  final int currentSourceIndex;
  final Function(int) onSourceSelected;

  const SourceSelectionWidget({
    super.key,
    required this.videoItem,
    required this.currentSourceIndex,
    required this.onSourceSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (videoItem.sources.length <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: PlayerTheme.backgroundColor.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.video_library,
                color: PlayerTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Video Sources',
                style: PlayerTheme.titleTextStyle.copyWith(fontSize: 16),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...videoItem.sources.asMap().entries.map((entry) {
            final index = entry.key;
            final source = entry.value;
            final isActive = index == currentSourceIndex;

            return _buildSourceItem(
              context,
              source,
              index,
              isActive,
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSourceItem(
    BuildContext context,
    VideoSource source,
    int index,
    bool isActive,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isActive ? null : () => onSourceSelected(index),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isActive
                  ? PlayerTheme.primaryColor.withValues(alpha: 0.2)
                  : Colors.transparent,
              border: Border.all(
                color: isActive
                    ? PlayerTheme.primaryColor
                    : PlayerTheme.textSecondary.withValues(alpha: 0.3),
                width: isActive ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                // Source indicator
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: isActive
                        ? PlayerTheme.primaryColor
                        : PlayerTheme.textSecondary.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),

                // Source info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        source.displayName,
                        style: PlayerTheme.titleTextStyle.copyWith(
                          fontSize: 14,
                          color: isActive
                              ? PlayerTheme.primaryColor
                              : PlayerTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 2),
                      const SizedBox.shrink(),
                    ],
                  ),
                ),

                // Active indicator
                if (isActive)
                  const Icon(
                    Icons.play_circle_filled,
                    color: PlayerTheme.primaryColor,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Bottom sheet to show source selection
class SourceSelectionBottomSheet extends StatelessWidget {
  final PresetItem videoItem;
  final int currentSourceIndex;
  final Function(int) onSourceSelected;

  const SourceSelectionBottomSheet({
    super.key,
    required this.videoItem,
    required this.currentSourceIndex,
    required this.onSourceSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: PlayerTheme.backgroundColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: PlayerTheme.textSecondary.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: SourceSelectionWidget(
              videoItem: videoItem,
              currentSourceIndex: currentSourceIndex,
              onSourceSelected: (index) {
                onSourceSelected(index);
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      ),
    );
  }

  static void show(
    BuildContext context,
    PresetItem videoItem,
    int currentSourceIndex,
    Function(int) onSourceSelected,
  ) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => SourceSelectionBottomSheet(
        videoItem: videoItem,
        currentSourceIndex: currentSourceIndex,
        onSourceSelected: onSourceSelected,
      ),
    );
  }
}
