import 'dart:async';
import 'package:flutter/foundation.dart';

class SessionTrackingService {
  static final SessionTrackingService _instance = SessionTrackingService._internal();
  factory SessionTrackingService() => _instance;
  SessionTrackingService._internal();

  static SessionTrackingService get instance => _instance;

  DateTime? _sessionStartTime;
  DateTime? _currentVideoStartTime;
  Duration _totalWatchTime = Duration.zero;
  Timer? _trackingTimer;
  bool _isVideoPlaying = false;

  // Getters
  Duration get totalWatchTime => _totalWatchTime;
  Duration get currentSessionDuration {
    if (_sessionStartTime == null) return Duration.zero;
    return DateTime.now().difference(_sessionStartTime!);
  }
  
  Duration get currentVideoDuration {
    if (_currentVideoStartTime == null) return Duration.zero;
    return DateTime.now().difference(_currentVideoStartTime!);
  }

  bool get isLongSession => totalWatchTime.inMinutes >= 15;
  bool get isVideoPlaying => _isVideoPlaying;

  /// Start a new session
  void startSession() {
    debugPrint('SessionTrackingService: Starting new session');
    _sessionStartTime = DateTime.now();
    _totalWatchTime = Duration.zero;
    _startTrackingTimer();
  }

  /// Start tracking a new video
  void startVideo() {
    debugPrint('SessionTrackingService: Starting video tracking');
    _currentVideoStartTime = DateTime.now();
    _isVideoPlaying = true;
  }

  /// Pause video tracking
  void pauseVideo() {
    debugPrint('SessionTrackingService: Pausing video tracking');
    _isVideoPlaying = false;
    _updateWatchTime();
  }

  /// Resume video tracking
  void resumeVideo() {
    debugPrint('SessionTrackingService: Resuming video tracking');
    _currentVideoStartTime = DateTime.now();
    _isVideoPlaying = true;
  }

  /// Stop current video tracking
  void stopVideo() {
    debugPrint('SessionTrackingService: Stopping video tracking');
    _isVideoPlaying = false;
    _updateWatchTime();
    _currentVideoStartTime = null;
  }

  /// End the current session
  void endSession() {
    debugPrint('SessionTrackingService: Ending session');
    _updateWatchTime();
    _stopTrackingTimer();
    
    debugPrint('SessionTrackingService: Total watch time: ${_totalWatchTime.inMinutes} minutes');
    debugPrint('SessionTrackingService: Session duration: ${currentSessionDuration.inMinutes} minutes');
    
    _sessionStartTime = null;
    _currentVideoStartTime = null;
    _totalWatchTime = Duration.zero;
    _isVideoPlaying = false;
  }

  /// Update the total watch time
  void _updateWatchTime() {
    if (_currentVideoStartTime != null && _isVideoPlaying) {
      final videoDuration = DateTime.now().difference(_currentVideoStartTime!);
      _totalWatchTime += videoDuration;
      debugPrint('SessionTrackingService: Updated total watch time to ${_totalWatchTime.inMinutes} minutes');
    }
  }

  /// Start the periodic tracking timer
  void _startTrackingTimer() {
    _trackingTimer?.cancel();
    _trackingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (_isVideoPlaying) {
        _updateWatchTime();
        _currentVideoStartTime = DateTime.now(); // Reset the start time for next interval
      }
    });
  }

  /// Stop the tracking timer
  void _stopTrackingTimer() {
    _trackingTimer?.cancel();
    _trackingTimer = null;
  }

  /// Get session statistics
  Map<String, dynamic> getSessionStats() {
    return {
      'totalWatchTime': _totalWatchTime.inMinutes,
      'sessionDuration': currentSessionDuration.inMinutes,
      'currentVideoDuration': currentVideoDuration.inMinutes,
      'isLongSession': isLongSession,
      'isVideoPlaying': _isVideoPlaying,
      'sessionStartTime': _sessionStartTime?.toIso8601String(),
    };
  }

  /// Reset all tracking data
  void reset() {
    debugPrint('SessionTrackingService: Resetting all data');
    _stopTrackingTimer();
    _sessionStartTime = null;
    _currentVideoStartTime = null;
    _totalWatchTime = Duration.zero;
    _isVideoPlaying = false;
  }

  /// Dispose resources
  void dispose() {
    debugPrint('SessionTrackingService: Disposing');
    _stopTrackingTimer();
  }
}
